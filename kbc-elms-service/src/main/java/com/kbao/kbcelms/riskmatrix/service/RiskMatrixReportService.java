package com.kbao.kbcelms.riskmatrix.service;

import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcelms.common.config.ElmsContext;
import com.kbao.kbcelms.common.nosql.service.TenantMongoServiceImpl;
import com.kbao.kbcelms.riskmatrix.bean.RiskMatrixResultDTO;
import com.kbao.kbcelms.riskmatrix.dao.RiskMatrixReportDao;
import com.kbao.kbcelms.riskmatrix.model.RiskMatrixReport;
import com.kbao.kbcelms.riskmatrix.vo.RiskMatrixResultVO;
import com.kbao.kbcelms.ufs.KbcUfsService;
import com.kbao.kbcelms.util.PdfUtils;
import com.kbao.kbcufs.enums.FileTypeEnum;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.EmptyUtils;
import com.kbao.tool.util.IDUtils;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 风险矩阵报告MongoDB Service
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Slf4j
@Service
public class RiskMatrixReportService extends TenantMongoServiceImpl<RiskMatrixReport, String, RiskMatrixReportDao> {


    @Autowired
    private KbcUfsService kbcUfsService;

    @Autowired
    @Lazy
    private RiskMatrixService riskMatrixService;

    private static final String APP_CODE = "elmsWeb";


    /**
     * 保存风险矩阵报告
     * 
     * @param report 报告实体
     * @return 保存后的报告
     */
    public RiskMatrixReport saveReport(RiskMatrixReport report) {
        try {
            // 设置基本信息
            if (EmptyUtils.isEmpty(report.getId())) {
                report.setId(IDUtils.generateBizId("RMR"));
            }
            
            // 设置创建信息
            Date now = DateUtils.getCurrentDate();
            String userId = ElmsContext.getUser().getUserId();
            String userName = ElmsContext.getUser().getUserName();
            String tenantId = ElmsContext.getTenantId();
            
            if (EmptyUtils.isEmpty(report.getCreateTime())) {
                report.setCreateTime(now);
                report.setCreatorId(userId);
                report.setCreatorName(userName);
            }
            
            report.setUpdateTime(now);
            report.setUpdaterId(userId);
            report.setUpdaterName(userName);
            report.setTenantId(tenantId);
            
            // 设置默认状态
            if (EmptyUtils.isEmpty(report.getReportStatus())) {
                report.setReportStatus("COMPLETED");
            }
            
            // 设置默认版本号
            if (EmptyUtils.isEmpty(report.getReportVersion())) {
                report.setReportVersion("1.0");
            }
            
            return this.save(report);
            
        } catch (Exception e) {
            log.error("保存风险矩阵报告失败，enterpriseId: {}", report.getEnterpriseId(), e);
            throw new BusinessException("保存风险矩阵报告失败: " + e.getMessage());
        }
    }

    /**
     * 根据企业ID查询风险矩阵报告列表
     * 
     * @param enterpriseId 企业ID
     * @return 报告列表
     */
    public List<RiskMatrixReport> getReportsByEnterpriseId(Long enterpriseId) {
        if (enterpriseId == null || enterpriseId <= 0) {
            throw new BusinessException("企业ID不能为空或无效");
        }
        
        String tenantId = SysLoginUtils.getUser().getTenantId();
        return dao.findByEnterpriseId(enterpriseId, tenantId);
    }

    /**
     * 根据企业ID查询最新的风险矩阵报告
     * 
     * @param enterpriseId 企业ID
     * @return 最新报告
     */
    public RiskMatrixReport getLatestReportByEnterpriseId(Long enterpriseId) {
        
        String tenantId = ElmsContext.getTenantId();
        return dao.findLatestByEnterpriseId(enterpriseId, tenantId);
    }

    /**
     * 根据企业ID查询最新的风险矩阵报告
     *
     * @param enterpriseId 企业ID
     * @return 最新报告
     */
    public RiskMatrixResultVO getRadarChartByEnterpriseId(Long enterpriseId) {
        RiskMatrixResultVO resultVO = new RiskMatrixResultVO();
        String tenantId = ElmsContext.getTenantId();
        RiskMatrixReport riskMatrixReport = dao.findRadarChartByEnterpriseId(enterpriseId, tenantId);
        if (EmptyUtils.isNotEmpty(riskMatrixReport)) {
            resultVO.setId(riskMatrixReport.getId());
            resultVO.setReportUrl(riskMatrixReport.getReportUrl());
            resultVO.setReportTime(riskMatrixReport.getReportTime());
            resultVO.setResultDTOS(riskMatrixReport.getRadarChartDatas());
            return  resultVO;
        }else {
            //补偿机制，如果没有则重新生成
            log.info("正在重新生成报告：enterpriseId:{}", enterpriseId);
            return riskMatrixService.loadRisMatrixReport(enterpriseId, tenantId);
        }
    }

    /**
     * 根据企业ID和报告状态查询风险矩阵报告列表
     * 
     * @param enterpriseId 企业ID
     * @param reportStatus 报告状态
     * @return 报告列表
     */
    public List<RiskMatrixReport> getReportsByEnterpriseIdAndStatus(Long enterpriseId, String reportStatus) {
        if (enterpriseId == null || enterpriseId <= 0) {
            throw new BusinessException("企业ID不能为空或无效");
        }
        
        String tenantId = SysLoginUtils.getUser().getTenantId();
        return dao.findByEnterpriseIdAndStatus(enterpriseId, reportStatus, tenantId);
    }

    /**
     * 更新报告状态
     * 
     * @param reportId 报告ID
     * @param reportStatus 新状态
     * @return 更新后的报告
     */
    public RiskMatrixReport updateReportStatus(String reportId, String reportStatus) {
        if (EmptyUtils.isEmpty(reportId)) {
            throw new BusinessException("报告ID不能为空");
        }
        
        RiskMatrixReport report = this.findById(reportId);
        if (report == null) {
            throw new BusinessException("报告不存在");
        }
        
        report.setReportStatus(reportStatus);
        report.setUpdateTime(DateUtils.getCurrentDate());
        report.setUpdaterId(ElmsContext.getUser().getUserId());
        report.setUpdaterName(ElmsContext.getUser().getUserName());
        
        return this.saveOrUpdate(report);
    }

    /**
     * 统计企业的风险矩阵报告数量
     * 
     * @param enterpriseId 企业ID
     * @return 报告数量
     */
    public long countReportsByEnterpriseId(Long enterpriseId) {
        if (enterpriseId == null || enterpriseId <= 0) {
            throw new BusinessException("企业ID不能为空或无效");
        }
        
        String tenantId = SysLoginUtils.getUser().getTenantId();
        return dao.countByEnterpriseId(enterpriseId, tenantId);
    }

    /**
     * 生成风险矩阵PDF报告并上传到OSS
     *
     * @param enterpriseId 企业ID
     * @return OSS文件下载链接
     */
    public String generatePdfReport(Long enterpriseId, String enterpriseType, List<RiskMatrixResultDTO> resultDTOS) {
        try {
            log.info("开始生成风险矩阵PDF报告，企业ID：{}, 企业类型：{}", enterpriseId, enterpriseType);

            // 1. 获取风险矩阵报告数据
//            RiskMatrixResultVO reportData = getRadarChartByEnterpriseId(enterpriseId);
//            if (EmptyUtils.isEmpty(reportData)) {
//                throw new BusinessException("未找到该企业的风险矩阵报告数据");
//            }
//            if (EmptyUtils.isNotEmpty(reportData)){
//                log.info("风险矩阵PDF报告已存在，企业ID：{}, 文件路径：{}", enterpriseId, reportData.getReportUrl());
//                return reportData.getReportUrl();
//            }

            // 2. 构建PDF内容
            PdfUtils.PdfContent pdfContent = buildPdfContent(resultDTOS, enterpriseId);

            // 3. 生成PDF文件名
            String fileName = generatePdfFileName(enterpriseId);

            // 4. 生成PDF MultipartFile
            MultipartFile pdfFile = PdfUtils.generateComplexPdfMultipartFile(pdfContent, fileName);

            // 5. 上传PDF到OSS
            FileUploadResponse uploadResponse = uploadPdfToOss(pdfFile, fileName);

            log.info("风险矩阵PDF报告生成成功，企业ID：{}, 文件路径：{}", enterpriseId, uploadResponse.getAbsolutePath());
            return uploadResponse.getAbsolutePath();

        } catch (Exception e) {
            log.error("生成风险矩阵PDF报告失败，企业ID：{}", enterpriseId, e);
            throw new BusinessException("PDF报告生成失败：" + e.getMessage());
        }
    }

    /**
     * 构建PDF内容
     *
     * @param reportData 风险矩阵报告数据
     * @param enterpriseId 企业ID
     * @return PDF内容配置
     */
    private PdfUtils.PdfContent buildPdfContent(List<RiskMatrixResultDTO> reportData, Long enterpriseId) {
        PdfUtils.PdfContent pdfContent = new PdfUtils.PdfContent();

        // 设置PDF标题
        String title = "企业风险矩阵评估报告";
        pdfContent.setTitle(title);

        // 构建PDF章节
        List<PdfUtils.PdfSection> sections = new ArrayList<>();

        // 1. 报告基本信息章节
        sections.add(buildBasicInfoSection(enterpriseId));

        // 2. 风险矩阵详情章节
        for (RiskMatrixResultDTO matrix : reportData) {
            sections.add(buildMatrixSection(matrix));
        }

        // 3. 风险评估汇总章节
        sections.add(buildSummarySection(reportData));

        pdfContent.setSections(sections);
        return pdfContent;
    }

    /**
     * 构建基本信息章节
     */
    private PdfUtils.PdfSection buildBasicInfoSection(Long enterpriseId) {
        PdfUtils.PdfSection section = new PdfUtils.PdfSection();
        section.setSectionTitle("一、报告基本信息");

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss");
        String currentTime = dateFormat.format(new Date());

        StringBuilder content = new StringBuilder();
        content.append("企业ID：").append(enterpriseId).append("\n");
        content.append("报告生成时间：").append(currentTime).append("\n");
        content.append("报告类型：风险矩阵评估报告\n");
        content.append("评估说明：本报告基于企业填写的问卷数据，通过风险矩阵模型进行综合评估分析。\n\n");

        section.setContent(content.toString());
        return section;
    }

    /**
     * 构建风险矩阵章节
     */
    private PdfUtils.PdfSection buildMatrixSection(RiskMatrixResultDTO matrix) {
        PdfUtils.PdfSection section = new PdfUtils.PdfSection();
        section.setSectionTitle("二、" + matrix.getName() + " 评估结果");

        // 构建表格数据
        List<String> headers = Arrays.asList("风险类别", "计算得分", "风险档次", "档次描述", "分数范围");
        List<List<String>> tableData = new ArrayList<>();

        if (EmptyUtils.isNotEmpty(matrix.getRiskMatrixLevels())) {
            for (RiskMatrixResultDTO.RiskMatrixLevelVO level : matrix.getRiskMatrixLevels()) {
                List<String> row = Arrays.asList(
                    level.getCategoryName() != null ? level.getCategoryName() : "",
                    level.getScore() != null ? level.getScore().toString() : "0",
                    level.getLevelName() != null ? level.getLevelName() : "",
                    level.getLevelDescription() != null ? level.getLevelDescription() : "",
                    level.getScoreRange() != null ? level.getScoreRange() : ""
                );
                tableData.add(row);
            }
        }

        section.setTableHeaders(headers);
        section.setTableData(tableData);

        return section;
    }

    /**
     * 构建汇总章节
     */
    private PdfUtils.PdfSection buildSummarySection(List<RiskMatrixResultDTO> reportData) {
        PdfUtils.PdfSection section = new PdfUtils.PdfSection();
        section.setSectionTitle("三、风险评估汇总");

        StringBuilder content = new StringBuilder();
        content.append("本次评估共涉及 ").append(reportData.size()).append(" 个风险矩阵。\n\n");

        // 统计各风险档次数量
        int highRiskCount = 0;
        int mediumRiskCount = 0;
        int lowRiskCount = 0;

        for (RiskMatrixResultDTO matrix : reportData) {
            if (EmptyUtils.isNotEmpty(matrix.getRiskMatrixLevels())) {
                for (RiskMatrixResultDTO.RiskMatrixLevelVO level : matrix.getRiskMatrixLevels()) {
                    String levelName = level.getLevelName();
                    if (levelName != null) {
                        if (levelName.contains("高") || levelName.contains("严重")) {
                            highRiskCount++;
                        } else if (levelName.contains("中") || levelName.contains("一般")) {
                            mediumRiskCount++;
                        } else if (levelName.contains("低") || levelName.contains("轻微")) {
                            lowRiskCount++;
                        }
                    }
                }
            }
        }

        content.append("风险档次分布：\n");
        content.append("• 高风险项目：").append(highRiskCount).append(" 项\n");
        content.append("• 中等风险项目：").append(mediumRiskCount).append(" 项\n");
        content.append("• 低风险项目：").append(lowRiskCount).append(" 项\n\n");

        content.append("建议：\n");
        if (highRiskCount > 0) {
            content.append("• 重点关注高风险项目，制定针对性的风险控制措施\n");
        }
        if (mediumRiskCount > 0) {
            content.append("• 对中等风险项目进行定期监控和评估\n");
        }
        content.append("• 持续完善风险管理体系，提升整体风险防控能力\n");

        section.setContent(content.toString());
        return section;
    }

    /**
     * 生成PDF文件名
     *
     * @param enterpriseId 企业ID
     * @return 文件名
     */
    private String generatePdfFileName(Long enterpriseId) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String timestamp = dateFormat.format(new Date());
        return String.format("风险矩阵评估报告_企业%s_%s.pdf", enterpriseId, timestamp);
    }

    /**
     * 上传PDF到OSS
     *
     * @param pdfFile PDF文件（MultipartFile类型）
     * @param fileName 文件名
     * @return 上传响应
     */
    private FileUploadResponse uploadPdfToOss(MultipartFile pdfFile, String fileName) {
        try {
            // 生成业务编号
            String businessNo = "RISK_MATRIX_PDF_" + System.currentTimeMillis();

            // 构建文件路径
            String filePath = "risk-matrix/reports/" + fileName;

            // 调用KbcUfsService上传文件
            FileUploadResponse response = kbcUfsService.upload(
                APP_CODE,                    // 应用编码
                businessNo,                  // 业务编号
                pdfFile,                     // MultipartFile对象
                null,                       // content为null，使用MultipartFile上传
                filePath,                   // 文件路径
                FileTypeEnum.FILE // 文件类型，使用UNKNOWN类型上传PDF
            );

            if (response == null) {
                throw new RuntimeException("文件上传响应为空");
            }

            log.info("PDF文件上传成功，文件名：{}, 文件大小：{} bytes, 绝对路径：{}",
                    fileName, pdfFile.getSize(), response.getAbsolutePath());
            return response;

        } catch (Exception e) {
            log.error("PDF文件上传失败，文件名：{}", fileName, e);
            throw new RuntimeException("PDF文件上传失败：" + e.getMessage(), e);
        }
    }
}
