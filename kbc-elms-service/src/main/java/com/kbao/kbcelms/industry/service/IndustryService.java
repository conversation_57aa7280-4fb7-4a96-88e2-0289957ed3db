package com.kbao.kbcelms.industry.service;

import com.kbao.kbcbsc.redis.util.RedisUtil;
import com.kbao.kbcbsc.service.sql.BaseSQLServiceImpl;
import com.kbao.kbcelms.industry.dao.IndustryMapper;
import com.kbao.kbcelms.industry.entity.Industry;
import com.kbao.kbcelms.industry.vo.IndustryTreeVO;
import com.kbao.kbcelms.util.ElmsRedisKeyUtil;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 行业分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class IndustryService extends BaseSQLServiceImpl<Industry, Integer, IndustryMapper> {

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 行业树缓存key前缀
     */
    private static final String INDUSTRY_TREE_CACHE_PREFIX = "industry:tree:cache";

    /**
     * 缓存过期时间：24小时（86400秒）
     */
    private static final int CACHE_EXPIRE_TIME = 86400;

    /**
     * 检查行业代码是否存在
     * @param code 行业代码
     * @param id 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    public boolean isExistCode(String code, Integer id) {
        int count = mapper.isExistCode(code, id);
        return count > 0;
    }

    /**
     * 根据行业代码查询行业信息
     * @param code 行业代码
     * @return 行业信息
     */
    public Industry selectByCode(String code) {
        return mapper.selectByCode(code);
    }

    /**
     * 根据父级代码查询子级行业列表
     * @param parentCode 父级行业代码
     * @return 子级行业列表
     */
    public List<Industry> selectByParentCode(String parentCode) {
        return mapper.selectByParentCode(parentCode);
    }

    /**
     * 根据级别查询行业列表
     * @param level 行业级别
     * @return 行业列表
     */
    public List<Industry> selectByLevel(Integer level) {
        return mapper.selectByLevel(level);
    }

    /**
     * 查询所有行业并组装为树形结构（带缓存）
     * @return 行业树形结构列表
     */
    public List<IndustryTreeVO> getIndustryTree() {
        // 先从缓存中获取
        String cacheKey = ElmsRedisKeyUtil.generateKey(INDUSTRY_TREE_CACHE_PREFIX);
        List<IndustryTreeVO> cachedResult = (List<IndustryTreeVO>) redisUtil.get(cacheKey);
        if (cachedResult != null && !cachedResult.isEmpty()) {
            logger.info("从缓存中获取行业树数据，缓存key: {}", cacheKey);
            return cachedResult;
        }
        
        // 缓存中没有数据，从数据库查询并构建树形结构
        logger.info("缓存中无数据，从数据库查询行业树数据");
        List<IndustryTreeVO> result = buildIndustryTreeFromDatabase();
        
        // 将结果存入缓存
        if (result != null && !result.isEmpty()) {
            redisUtil.set(cacheKey, result, CACHE_EXPIRE_TIME);
            logger.info("行业树数据已存入缓存，缓存key: {}，过期时间: {}秒", cacheKey, CACHE_EXPIRE_TIME);
        }
        
        return result;
    }
    
    /**
     * 从数据库构建行业树形结构
     * @return 行业树形结构列表
     */
    private List<IndustryTreeVO> buildIndustryTreeFromDatabase() {
        List<Industry> all = this.selectByParam(null);
        Map<String, IndustryTreeVO> codeMap = new HashMap<>();
        List<IndustryTreeVO> roots = new ArrayList<>();
        
        // 创建所有节点，过滤掉第一级节点
        for (Industry industry : all) {
            // 跳过第一级节点
            if (industry.getLevel() == 1) {
                continue;
            }
            
            IndustryTreeVO node = new IndustryTreeVO(
                industry.getCode(), 
                industry.getName(),
                industry.getLevel(),
                industry.getFullPath(),
                industry.getFullName()
            );
            codeMap.put(industry.getCode(), node);
        }
        
        // 构建树形结构
        for (Industry industry : all) {
            // 跳过第一级节点
            if (industry.getLevel() == 1) {
                continue;
            }
            
            String parentCode = industry.getParentCode();
            if (parentCode == null || parentCode.isEmpty() || !codeMap.containsKey(parentCode)) {
                roots.add(codeMap.get(industry.getCode()));
            } else {
                codeMap.get(parentCode).getChildren().add(codeMap.get(industry.getCode()));
            }
        }
        return roots;
    }

    /**
     * 清空所有行业数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void truncateTable() {
        mapper.truncateTable();
        // 清除缓存
        clearIndustryTreeCache();
    }
    
    /**
     * 清除行业树缓存
     */
    public void clearIndustryTreeCache() {
        String cacheKey = ElmsRedisKeyUtil.generateKey(INDUSTRY_TREE_CACHE_PREFIX);
        redisUtil.del(cacheKey);
        logger.info("已清除行业树缓存，缓存key: {}", cacheKey);
    }
    


    /**
     * 导入行业数据
     * @param file 导入的Excel文件
     * @return 导入结果信息
     */
    @Transactional(rollbackFor = Exception.class)
    public String importIndustryData(MultipartFile file) {
        try {
            // 清空现有数据
            truncateTable();

            // 解析Excel文件
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            List<Industry> industryList = new ArrayList<>();
            int successCount = 0;
            int skipCount = 0;

            // 用于存储当前层级的代码，便于填充空白单元格
            String[] currentCodes = new String[4]; // 门类、大类、中类、小类
            String[] currentNames = new String[4]; // 对应的名称

            // 从第2行开始读取数据（第1行是标题）
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                try {
                    Industry industry = parseRowToIndustryWithContext(row, currentCodes, currentNames);
                    if (industry != null) {
                        industryList.add(industry);
                        successCount++;
                    } else {
                        skipCount++;
                    }
                } catch (Exception e) {
                    logger.error("importIndustryData error", e);
                    skipCount++;
                }
            }
            // 批量插入数据
            if (!industryList.isEmpty()) {
                batchInsert(industryList, 200);
            }

            workbook.close();
            
            // 导入完成后清除缓存，下次查询时会重新构建缓存
            clearIndustryTreeCache();

            return String.format("导入完成！清空原数据，成功导入 %d 条数据，跳过 %d 条数据", successCount, skipCount);

        } catch (Exception e) {
            throw new RuntimeException("导入失败：" + e.getMessage(), e);
        }
    }

    /**
     * 获取单元格字符串值
     * @param cell 单元格
     * @return 字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellTypeEnum()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 解析Excel行数据为Industry对象（带上下文填充）
     * @param row Excel行数据
     * @param currentCodes 当前各级别代码数组
     * @param currentNames 当前各级别名称数组
     * @return Industry对象
     */
    private Industry parseRowToIndustryWithContext(Row row, String[] currentCodes, String[] currentNames) {
        // 读取各列的值
        String col0 = getCellStringValue(row.getCell(0)).trim(); // A列 - 门类代码
        String col1 = getCellStringValue(row.getCell(1)).trim(); // B列 - 大类代码
        String col2 = getCellStringValue(row.getCell(2)).trim(); // C列 - 中类代码
        String col3 = getCellStringValue(row.getCell(3)).trim(); // D列 - 小类代码
        String col4 = getCellStringValue(row.getCell(4)).trim(); // E列 - 名称

        // 跳过标题行（门类,大类,中类,小类）
        if ("门类".equals(col0) || "大类".equals(col1) || "中类".equals(col2) || "小类".equals(col3)) {
            return null;
        }
        // 确定当前行的级别和代码
        String code = null;
        String name = col4;
        int level = 0;

        if (!col0.isEmpty()) {
            // 门类
            code = col0;
            level = 1;
            currentCodes[0] = col0;
            currentNames[0] = name;
            currentCodes[1] = currentCodes[2] = currentCodes[3] = null; // 清空下级
        } else if (!col1.isEmpty()) {
            // 大类
            code = col1;
            level = 2;
            currentCodes[1] = col1;
            currentNames[1] = name;
            currentCodes[2] = currentCodes[3] = null; // 清空下级
        } else if (!col2.isEmpty()) {
            // 中类
            code = col2;
            level = 3;
            currentCodes[2] = col2;
            currentNames[2] = name;
            currentCodes[3] = null; // 清空下级
        } else if (!col3.isEmpty()) {
            // 小类
            code = col3;
            level = 4;
            currentCodes[3] = col3;
            currentNames[3] = name;
        }

        // 如果没有有效代码，跳过
        if (code == null || code.isEmpty() || name.isEmpty()) {
            return null;
        }

        Industry industry = new Industry();
        industry.setCode(code);
        industry.setName(name);
        industry.setLevel(level);

        // 设置父级代码
        if (level > 1) {
            industry.setParentCode(currentCodes[level - 2]);
        }

        // 构建完整路径
        industry.setFullPath(buildFullPathFromContext(currentCodes, level));

        // 构建完整名称
        industry.setFullName(buildFullNameFromContext(currentNames, level));

        industry.setSortOrder(0);
        industry.setIsValid(1);
        industry.setCreateTime(new Date());

        return industry;
    }


    /**
     * 根据上下文构建完整路径
     * @param currentCodes 当前各级别代码数组
     * @param level 当前级别
     * @return 完整路径
     */
    private String buildFullPathFromContext(String[] currentCodes, int level) {
        StringBuilder path = new StringBuilder();
        for (int i = 0; i < level; i++) {
            if (currentCodes[i] != null) {
                if (path.length() > 0) {
                    path.append("/");
                }
                path.append(currentCodes[i]);
            }
        }
        return path.toString();
    }

    /**
     * 根据上下文构建完整名称
     * @param currentNames 当前各级别名称数组
     * @param level 当前级别
     * @return 完整名称
     */
    private String buildFullNameFromContext(String[] currentNames, int level) {
        StringBuilder fullName = new StringBuilder();
        for (int i = 0; i < level; i++) {
            if (currentNames[i] != null) {
                if (fullName.length() > 0) {
                    fullName.append("/");
                }
                fullName.append(currentNames[i]);
            }
        }
        return fullName.toString();
    }
}
