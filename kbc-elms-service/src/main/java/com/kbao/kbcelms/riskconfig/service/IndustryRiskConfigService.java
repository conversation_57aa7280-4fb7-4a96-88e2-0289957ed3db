package com.kbao.kbcelms.riskconfig.service;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.model.Pagination;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.kbcelms.common.config.ElmsContext;
import com.kbao.kbcelms.industry.entity.Industry;
import com.kbao.kbcelms.industry.service.IndustryService;
import com.kbao.kbcelms.riskmatrix.service.RiskMatrixReportService;
import com.kbao.kbcelms.riskmatrix.vo.RiskMatrixResultVO;
import com.kbao.kbcelms.ufs.KbcUfsService;
import com.kbao.kbcelms.util.PdfUtils;
import com.kbao.kbcufs.enums.FileTypeEnum;
import com.kbao.kbcufs.file.vo.client.FileUploadResponse;
import com.kbao.tool.util.EmptyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import com.kbao.kbcelms.riskconfig.bean.IndustryRiskConfigQuery;
import com.kbao.kbcelms.riskconfig.bean.IndustryRiskConfigRequest;
import com.kbao.kbcelms.riskconfig.constants.RiskConfigConstants;
import com.kbao.kbcelms.riskconfig.dao.IndustryRiskConfigDao;
import com.kbao.kbcelms.riskconfig.dao.RiskConfigHistoryDao;
import com.kbao.kbcelms.riskconfig.entity.DynamicColumnDef;
import com.kbao.kbcelms.riskconfig.entity.IndustryRiskConfig;
import com.kbao.kbcelms.riskconfig.entity.RiskConfigHistory;
import com.kbao.kbcelms.riskconfig.entity.RiskMatrixConfig;
import com.kbao.kbcelms.riskconfig.entity.RiskToolsConfig;
import com.kbao.kbcelms.riskconfig.util.RiskConfigConverter;
import com.kbao.kbcelms.riskconfig.util.RichTextContentUtils;
import com.kbao.kbcelms.riskconfig.vo.IndustryRiskConfigVO;
import com.kbao.tool.util.SysLoginUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 行业风险配置服务层
 * <AUTHOR>
 * @date 2025-08-11
 */
@Service
@Slf4j
public class IndustryRiskConfigService extends BaseMongoServiceImpl<IndustryRiskConfig, String, IndustryRiskConfigDao> {

    private static final Logger logger = LoggerFactory.getLogger(IndustryRiskConfigService.class);

    private static final String APP_CODE = "elmsWeb";

    @Autowired
    private RiskConfigHistoryDao riskConfigHistoryDao;

    @Autowired
    private IndustryService industryService;

    @Autowired
    private KbcUfsService kbcUfsService;

    /**
     * 分页查询行业风险配置列表
     */
    public PageInfo<IndustryRiskConfigVO> getPage(PageRequest<IndustryRiskConfigQuery> request) {
        IndustryRiskConfigQuery queryParam = request.getParam();
        if (queryParam == null) {
            queryParam = new IndustryRiskConfigQuery();
        }

        // 获取当前用户租户ID
        String tenantId = queryParam.getTenantId();
        if (!StringUtils.hasText(tenantId)) {
            tenantId = getCurrentTenantId();
        }

        // 构建MongoDB查询条件
        Query mongoQuery = buildQuery(queryParam, tenantId);

        // 构建分页参数
        Pagination<IndustryRiskConfig> pagination = new Pagination<>(
                request.getPageNum(),
                request.getPageSize(),
                buildSortString(queryParam)
        );

        // 使用父类的分页方法
        PageInfo<IndustryRiskConfig> entityPageInfo = super.page(mongoQuery, pagination);

        // 转换为VO
        List<IndustryRiskConfigVO> vos = RiskConfigConverter.entityListToVOList(entityPageInfo.getList());

        // 构建VO分页结果
        PageInfo<IndustryRiskConfigVO> voPageInfo = new PageInfo<>();
        voPageInfo.setList(vos);
        voPageInfo.setTotal(entityPageInfo.getTotal());
        voPageInfo.setPageNum(entityPageInfo.getPageNum());
        voPageInfo.setPageSize(entityPageInfo.getPageSize());
        voPageInfo.setPages(entityPageInfo.getPages());
        voPageInfo.setSize(entityPageInfo.getSize());
        voPageInfo.setStartRow(entityPageInfo.getStartRow());
        voPageInfo.setEndRow(entityPageInfo.getEndRow());
        voPageInfo.setHasNextPage(entityPageInfo.isHasNextPage());
        voPageInfo.setHasPreviousPage(entityPageInfo.isHasPreviousPage());
        voPageInfo.setIsFirstPage(entityPageInfo.isIsFirstPage());
        voPageInfo.setIsLastPage(entityPageInfo.isIsLastPage());

        return voPageInfo;
    }

    /**
     * 构建MongoDB查询条件
     */
    private Query buildQuery(IndustryRiskConfigQuery queryParam, String tenantId) {
        Criteria criteria = Criteria.where("deleted").is(false);

        // 租户隔离
        if (StringUtils.hasText(tenantId)) {
            criteria.and("tenantId").is(tenantId);
        }

        // 一级行业编码
        if (StringUtils.hasText(queryParam.getIndustryLevel1Code())) {
            criteria.and("industryLevel1Code").is(queryParam.getIndustryLevel1Code());
        }

        // 一级行业名称模糊查询
        if (StringUtils.hasText(queryParam.getIndustryLevel1Name())) {
            criteria.and("industryLevel1Name").regex(queryParam.getIndustryLevel1Name(), "i");
        }

        // 二级行业编码
        if (StringUtils.hasText(queryParam.getIndustryLevel2Code())) {
            criteria.and("industryLevel2Code").is(queryParam.getIndustryLevel2Code());
        }

        // 二级行业名称模糊查询
        if (StringUtils.hasText(queryParam.getIndustryLevel2Name())) {
            criteria.and("industryLevel2Name").regex(queryParam.getIndustryLevel2Name(), "i");
        }

        // 风险等级
        if (StringUtils.hasText(queryParam.getRiskLevel())) {
            criteria.and("riskLevel").is(queryParam.getRiskLevel());
        }

        // 状态
        if (StringUtils.hasText(queryParam.getStatus())) {
            criteria.and("status").is(queryParam.getStatus());
        }

        // 关键词搜索（支持一级行业名称、二级行业名称、矩阵描述、风险类型等）
        if (StringUtils.hasText(queryParam.getKeyword())) {
            Criteria keywordCriteria = new Criteria().orOperator(
                Criteria.where("industryLevel1Name").regex(queryParam.getKeyword(), "i"),
                Criteria.where("industryLevel2Name").regex(queryParam.getKeyword(), "i"),
                Criteria.where("matrixDesc").regex(queryParam.getKeyword(), "i"),
                Criteria.where("matrixConfig.rowData.type").regex(queryParam.getKeyword(), "i"),
                Criteria.where("matrixConfig.rowData.description").regex(queryParam.getKeyword(), "i"),
                Criteria.where("toolsConfig.rowData.name").regex(queryParam.getKeyword(), "i"),
                Criteria.where("toolsConfig.rowData.description").regex(queryParam.getKeyword(), "i")
            );
            criteria.andOperator(keywordCriteria);
        }

        // 创建时间范围
        if (queryParam.getCreatedTimeStart() != null) {
            criteria.and("createdTime").gte(queryParam.getCreatedTimeStart());
        }
        if (queryParam.getCreatedTimeEnd() != null) {
            criteria.and("createdTime").lte(queryParam.getCreatedTimeEnd());
        }

        return new Query(criteria);
    }

    /**
     * 构建排序字符串
     */
    private String buildSortString(IndustryRiskConfigQuery queryParam) {
        String sortField = queryParam.getSortField();
        String sortDirection = queryParam.getSortDirection();

        if (StringUtils.hasText(sortField)) {
            String direction = "desc".equalsIgnoreCase(sortDirection) ? "desc" : "asc";
            return sortField + " " + direction;
        }

        // 默认排序：按一级行业编码升序，创建时间降序
        return "industryLevel1Code asc, createdTime desc";
    }

    /**
     * 根据一级行业编码获取配置详情
     */
    public IndustryRiskConfigVO getByIndustryLevel1Code(String industryLevel1Code) {
        String tenantId = getCurrentTenantId();
        IndustryRiskConfig entity = this.dao.findByIndustryLevel1Code(industryLevel1Code, tenantId);
        if (entity == null) {
            return null;
        }

        return RiskConfigConverter.entityToVO(entity);
    }

    /**
     * 根据行业编码获取配置详情
     */
    public IndustryRiskConfigVO getByIndustryByCode(String categoryCode) {
        String tenantId = getCurrentTenantId();
        //根据企业id查询行业编码
        Industry industry = industryService.selectByCode(categoryCode);
        if (EmptyUtils.isEmpty(industry) || EmptyUtils.isEmpty(industry.getFullPath())) {
            throw new BusinessException("行业编码无效");
        }
        String fullPath = industry.getFullPath();
        String[] fullPathArray = fullPath.split("/");
        if (fullPathArray.length < 2) {
            throw new BusinessException("一级行业编码不存在");
        }
        String industryLevel1Code = fullPathArray[1];

        IndustryRiskConfig entity = this.dao.findByIndustryLevel1Code(industryLevel1Code, tenantId);
        if (entity == null) {
            return null;
        }

        return RiskConfigConverter.entityToVO(entity);
    }

    /**
     * 根据ID获取配置详情
     */
    public IndustryRiskConfigVO getById(String id) {
        IndustryRiskConfig entity = this.dao.findById(id);
        if (entity == null || entity.getDeleted()) {
            return null;
        }

        return RiskConfigConverter.entityToVO(entity);
    }

    /**
     * 保存行业风险配置
     */
    @Transactional(rollbackFor = Exception.class)
    public IndustryRiskConfigVO save(IndustryRiskConfigRequest request) {
        String userId = getCurrentUserId();
        String tenantId = getCurrentTenantId();

        // 验证和清理富文本内容
        validateAndSanitizeRichTextContent(request);

        // 转换为实体
        IndustryRiskConfig entity = RiskConfigConverter.requestToEntity(request);
        entity.setTenantId(tenantId);

        IndustryRiskConfig oldEntity = null;
        boolean isUpdate = StringUtils.hasText(entity.getId());

        if (isUpdate) {
            // 更新操作
            oldEntity = this.dao.findById(entity.getId());
            if (oldEntity == null || oldEntity.getDeleted()) {
                throw new RuntimeException("配置不存在");
            }

            // 检查一级行业编码是否重复
            if (this.dao.existsByIndustryLevel1Code(entity.getIndustryLevel1Code(), entity.getId(), tenantId)) {
                throw new RuntimeException("一级行业编码已存在");
            }

            // 生成新版本号
            String newVersion = RiskConfigConverter.generateNewVersion(oldEntity.getConfigVersion());
            entity.setConfigVersion(newVersion);
            entity.setUpdatedBy(userId);
            entity.setUpdatedTime(new Date());
            entity.setCreatedBy(oldEntity.getCreatedBy());
            entity.setCreatedTime(oldEntity.getCreatedTime());
        } else {
            // 新增操作
            // 检查一级行业编码是否重复
            if (this.dao.existsByIndustryLevel1Code(entity.getIndustryLevel1Code(), null, tenantId)) {
                throw new RuntimeException("一级行业编码已存在");
            }

            entity.setConfigVersion(RiskConfigConstants.DefaultConfig.DEFAULT_VERSION);
            entity.setCreatedBy(userId);
            entity.setCreatedTime(new Date());
            entity.setUpdatedBy(userId);
            entity.setUpdatedTime(new Date());
        }
        //生成pdf报告
        try {
            entity.setUrl(generatePdfReport(entity));
        }catch (Exception e){
            log.error("生成pdf报告失败", e);
        }

        IndustryRiskConfig savedEntity = this.dao.saveOrUpdate(entity);

        // 记录变更历史
        recordHistory(savedEntity, oldEntity, request.getChangeSummary(), userId, isUpdate);

        return RiskConfigConverter.entityToVO(savedEntity);
    }

    /**
     * 更新行业风险配置（兼容旧接口）
     */
//    @Transactional(rollbackFor = Exception.class)
//    public IndustryRiskConfigVO updateByIndustryCode(String industryCode, Map<String, Object> updateData) {
//        String userId = getCurrentUserId();
//        String tenantId = getCurrentTenantId();
//
//        // 查找现有配置
//        IndustryRiskConfig oldEntity = this.dao.findByIndustryCode(industryCode, tenantId);
//        if (oldEntity == null || oldEntity.getDeleted()) {
//            throw new RuntimeException("配置不存在");
//        }
//
//        // 更新数据
//        IndustryRiskConfig entity = new IndustryRiskConfig();
//        entity.setId(oldEntity.getId());
//        entity.setIndustryLevel1Code(oldEntity.getIndustryLevel1Code());
//        entity.setIndustryLevel1Name(oldEntity.getIndustryLevel1Name());
//        entity.setIndustryLevel2Code(oldEntity.getIndustryLevel2Code());
//        entity.setIndustryLevel2Name(oldEntity.getIndustryLevel2Name());
//        entity.setTenantId(oldEntity.getTenantId());
//        entity.setStatus(oldEntity.getStatus());
//        entity.setCreatedBy(oldEntity.getCreatedBy());
//        entity.setCreatedTime(oldEntity.getCreatedTime());
//
//        // 更新矩阵描述
//        if (updateData.containsKey("matrixDesc")) {
//            entity.setMatrixDesc((String) updateData.get("matrixDesc"));
//        } else {
//            entity.setMatrixDesc(oldEntity.getMatrixDesc());
//        }
//
//        // 更新配置数据
//        if (updateData.containsKey("matrixConfig")) {
//            entity.setMatrixConfig((RiskMatrixConfig) updateData.get("matrixConfig"));
//        } else {
//            entity.setMatrixConfig(oldEntity.getMatrixConfig());
//        }
//
////        if (updateData.containsKey("toolsConfig")) {
////            entity.setToolsConfig((RiskToolsConfig) updateData.get("toolsConfig"));
////        } else {
////            entity.setToolsConfig(oldEntity.getToolsConfig());
////        }
//
//        // 生成新版本号
//        String newVersion = RiskConfigConverter.generateNewVersion(oldEntity.getConfigVersion());
//        entity.setConfigVersion(newVersion);
//        entity.setUpdatedBy(userId);
//        entity.setUpdatedTime(new Date());
//
//        // 保存实体
//        IndustryRiskConfig savedEntity = this.dao.save(entity);
//
//        // 记录变更历史
//        Map<String, Object> changeContent = RiskConfigConverter.createChangeSnapshot(oldEntity, savedEntity);
//        RiskConfigHistory history = RiskConfigConverter.createHistory(
//                savedEntity.getId(),
//                newVersion,
//                RiskConfigConstants.ChangeType.UPDATE,
//                changeContent,
//                "通过旧接口更新配置",
//                userId
//        );
//        riskConfigHistoryDao.save(history);
//
//        return RiskConfigConverter.entityToVO(savedEntity);
//    }

    /**
     * 生成行业风险配置PDF报告并上传到OSS
     *
     * @param riskConfig 行业风险配置
     * @return OSS文件下载链接
     */
    public String generatePdfReport(IndustryRiskConfig riskConfig) {
        try {
            log.info("开始生成行业风险配置PDF报告，配置ID：{}", riskConfig.getId());

            // 1. 验证输入参数
            if (EmptyUtils.isEmpty(riskConfig)) {
                throw new BusinessException("行业风险配置不能为空");
            }

            // 2. 构建PDF内容
            PdfUtils.PdfContent pdfContent = buildPdfContent(riskConfig);

            // 3. 生成PDF文件名
            String fileName = generatePdfFileName(riskConfig);

            // 4. 生成PDF MultipartFile
            MultipartFile pdfFile = PdfUtils.generateComplexPdfMultipartFile(pdfContent, fileName);

            // 5. 上传PDF到OSS
            FileUploadResponse uploadResponse = uploadPdfToOss(pdfFile, fileName);

            log.info("行业风险配置PDF报告生成成功，配置ID：{}, 文件路径：{}",
                    riskConfig.getId(), uploadResponse.getAbsolutePath());
            return uploadResponse.getAbsolutePath();

        } catch (Exception e) {
            log.error("生成行业风险配置PDF报告失败，配置ID：{}", riskConfig.getId(), e);
            throw new BusinessException("PDF报告生成失败：" + e.getMessage());
        }
    }

    /**
     * 删除行业风险配置
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        String userId = getCurrentUserId();

        IndustryRiskConfig entity = this.dao.findById(id);
        if (entity == null || entity.getDeleted()) {
            throw new RuntimeException("配置不存在");
        }

        // 软删除
        entity.setDeleted(true);
        entity.setUpdatedBy(userId);
        entity.setUpdatedTime(new Date());
        this.dao.saveOrUpdate(entity);

        // 记录删除历史
        Map<String, Object> changeContent = new HashMap<>();
        changeContent.put("deletedId", id);
        changeContent.put("deletedIndustryCode", entity.getIndustryLevel1Code());
        changeContent.put("deletedIndustryName", entity.getIndustryLevel1Name());

        RiskConfigHistory history = RiskConfigConverter.createHistory(
                id,
                entity.getConfigVersion(),
                RiskConfigConstants.ChangeType.DELETE,
                changeContent,
                "删除配置",
                userId
        );
        riskConfigHistoryDao.save(history);
    }

    /**
     * 批量删除行业风险配置
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<String> ids) {
        String userId = getCurrentUserId();
        this.dao.batchDelete(ids, userId);

        // 记录批量删除历史
        Map<String, Object> changeContent = new HashMap<>();
        changeContent.put("deletedIds", ids);
        changeContent.put("deletedCount", ids.size());

        RiskConfigHistory history = RiskConfigConverter.createHistory(
                "batch_" + System.currentTimeMillis(),
                "batch",
                RiskConfigConstants.ChangeType.DELETE,
                changeContent,
                "批量删除配置",
                userId
        );
        riskConfigHistoryDao.save(history);
    }

    /**
     * 获取行业列表
     */
    public List<Map<String, Object>> getIndustries(String tenantId) {
        if (!StringUtils.hasText(tenantId)) {
            tenantId = getCurrentTenantId();
        }

        List<IndustryRiskConfig> entities = this.dao.findAllIndustries(tenantId);

        return entities.stream().map(entity -> {
            Map<String, Object> industry = new HashMap<>();
            industry.put("level1Code", entity.getIndustryLevel1Code());
            industry.put("level1Name", entity.getIndustryLevel1Name());
            industry.put("level2Code", entity.getIndustryLevel2Code());
            industry.put("level2Name", entity.getIndustryLevel2Name());
            industry.put("hasConfig", true);
            return industry;
        }).collect(Collectors.toList());
    }

    /**
     * 获取统计信息
     */
    public Map<String, Object> getStatistics(String tenantId) {
        if (!StringUtils.hasText(tenantId)) {
            tenantId = getCurrentTenantId();
        }

        Map<String, Object> statistics = new HashMap<>();

        // 总配置数
        long totalConfigs = this.dao.countByPage(null, null, null,
                RiskConfigConstants.Status.ACTIVE, null, tenantId, null, null);
        statistics.put("totalConfigs", totalConfigs);

        // 各风险等级统计
        long highRiskCount = this.dao.countByRiskLevel(RiskConfigConstants.RiskLevel.HIGH, tenantId);
        long mediumRiskCount = this.dao.countByRiskLevel(RiskConfigConstants.RiskLevel.MEDIUM, tenantId);
        long lowRiskCount = this.dao.countByRiskLevel(RiskConfigConstants.RiskLevel.LOW, tenantId);
        long criticalRiskCount = this.dao.countByRiskLevel(RiskConfigConstants.RiskLevel.CRITICAL, tenantId);

        statistics.put("highRiskCount", highRiskCount);
        statistics.put("mediumRiskCount", mediumRiskCount);
        statistics.put("lowRiskCount", lowRiskCount);
        statistics.put("criticalRiskCount", criticalRiskCount);

        // 风险等级分布
        List<Map<String, Object>> distribution = new ArrayList<>();
        if (totalConfigs > 0) {
            distribution.add(createDistributionItem("high", highRiskCount, totalConfigs));
            distribution.add(createDistributionItem("medium", mediumRiskCount, totalConfigs));
            distribution.add(createDistributionItem("low", lowRiskCount, totalConfigs));
            distribution.add(createDistributionItem("critical", criticalRiskCount, totalConfigs));
        }
        statistics.put("riskLevelDistribution", distribution);

        return statistics;
    }

    /**
     * 获取配置变更历史
     */
    public PageInfo<RiskConfigHistory> getHistory(String id, int pageNum, int pageSize) {
        List<RiskConfigHistory> histories = riskConfigHistoryDao.findByConfigId(id, pageNum, pageSize);
        long total = riskConfigHistoryDao.countByConfigId(id);

        PageInfo<RiskConfigHistory> pageInfo = new PageInfo<>();
        pageInfo.setList(histories);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages((int) Math.ceil((double) total / pageSize));

        return pageInfo;
    }

    // ==================== 私有方法 ====================

    /**
     * 记录变更历史
     */
    private void recordHistory(IndustryRiskConfig newEntity, IndustryRiskConfig oldEntity,
                              String changeSummary, String userId, boolean isUpdate) {
        String changeType = isUpdate ? RiskConfigConstants.ChangeType.UPDATE : RiskConfigConstants.ChangeType.CREATE;
        Map<String, Object> changeContent = RiskConfigConverter.createChangeSnapshot(oldEntity, newEntity);

        RiskConfigHistory history = RiskConfigConverter.createHistory(
                newEntity.getId(),
                newEntity.getConfigVersion(),
                changeType,
                changeContent,
                changeSummary != null ? changeSummary : (isUpdate ? "更新配置" : "创建配置"),
                userId
        );

        riskConfigHistoryDao.save(history);
    }

    /**
     * 创建分布统计项
     */
    private Map<String, Object> createDistributionItem(String level, long count, long total) {
        Map<String, Object> item = new HashMap<>();
        item.put("level", level);
        item.put("count", count);
        item.put("percentage", Math.round((double) count / total * 100));
        return item;
    }

    /**
     * 验证和清理富文本内容
     */
    private void validateAndSanitizeRichTextContent(IndustryRiskConfigRequest request) {
        if (request.getRichTextContent() != null) {
            // 验证内容
            RichTextContentUtils.validateContent(request.getRichTextContent());

            // 清理危险脚本
            String sanitizedContent = RichTextContentUtils.sanitizeContent(request.getRichTextContent());
            request.setRichTextContent(sanitizedContent);

            // 记录内容统计信息
            RichTextContentUtils.ContentStats stats = RichTextContentUtils.getContentStats(sanitizedContent);
            logger.info("富文本内容统计: {}", stats);
        }
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        try {
            return SysLoginUtils.getUserId();
        } catch (Exception e) {
            logger.warn("获取当前用户ID失败，使用默认值", e);
            return "system";
        }
    }

    /**
     * 获取当前租户ID
     */
    private String getCurrentTenantId() {
        return ElmsContext.getTenantId();
    }

    /**
     * 验证一级行业编码格式
     *
     * @param industryLevel1Code 一级行业编码
     * @throws RuntimeException 如果格式不正确
     */
    public void validateIndustryLevel1CodeFormat(String industryLevel1Code) {
        if (!StringUtils.hasText(industryLevel1Code)) {
            throw new RuntimeException("一级行业编码不能为空");
        }

        String trimmedCode = industryLevel1Code.trim();

        // 检查长度（1-10个字符）
        if (trimmedCode.length() < 1 || trimmedCode.length() > 10) {
            throw new RuntimeException("一级行业编码长度必须在1-10个字符之间");
        }

        // 检查格式（只允许字母、数字、下划线、中划线）
        if (!trimmedCode.matches("^[A-Za-z0-9_-]+$")) {
            throw new RuntimeException("一级行业编码只能包含字母、数字、下划线、中划线");
        }

        // 检查是否以字母开头
        if (!Character.isLetter(trimmedCode.charAt(0))) {
            throw new RuntimeException("一级行业编码必须以字母开头");
        }
    }

    /**
     * 构建PDF内容
     *
     * @param riskConfig 行业风险配置
     * @return PDF内容配置
     */
    private PdfUtils.PdfContent buildPdfContent(IndustryRiskConfig riskConfig) {
        PdfUtils.PdfContent pdfContent = new PdfUtils.PdfContent();

        // 设置PDF标题
        String title = String.format("行业风险配置报告 - %s", riskConfig.getIndustryLevel1Name());
        pdfContent.setTitle(title);

        // 构建PDF章节
        List<PdfUtils.PdfSection> sections = new ArrayList<>();

        // 1. 基本信息章节
        sections.add(buildBasicInfoSection(riskConfig));

        // 2. 风险矩阵配置章节
        if (riskConfig.getMatrixConfig() != null) {
            sections.add(buildMatrixConfigSection(riskConfig));
        }

        // 3. 富文本内容章节
        if (StringUtils.hasText(riskConfig.getRichTextContent())) {
            sections.add(buildRichTextSection(riskConfig));
        }

        pdfContent.setSections(sections);
        return pdfContent;
    }
    /**
     * 构建基本信息章节
     *
     * @param riskConfig 行业风险配置
     * @return PDF章节
     */
    private PdfUtils.PdfSection buildBasicInfoSection(IndustryRiskConfig riskConfig) {
        PdfUtils.PdfSection section = new PdfUtils.PdfSection();
        section.setSectionTitle("基本信息");

        List<String> content = new ArrayList<>();
        content.add("一级行业编码：" + (riskConfig.getIndustryLevel1Code() != null ? riskConfig.getIndustryLevel1Code() : ""));
        content.add("一级行业名称：" + (riskConfig.getIndustryLevel1Name() != null ? riskConfig.getIndustryLevel1Name() : ""));

        if (StringUtils.hasText(riskConfig.getIndustryLevel2Code())) {
            content.add("二级行业编码：" + riskConfig.getIndustryLevel2Code());
        }
        if (StringUtils.hasText(riskConfig.getIndustryLevel2Name())) {
            content.add("二级行业名称：" + riskConfig.getIndustryLevel2Name());
        }

        content.add("风险等级：" + (riskConfig.getRiskLevel() != null ? riskConfig.getRiskLevel() : ""));
        content.add("配置版本：" + (riskConfig.getConfigVersion() != null ? riskConfig.getConfigVersion() : ""));
        content.add("状态：" + (riskConfig.getStatus() != null ? riskConfig.getStatus() : ""));

        if (riskConfig.getCreatedTime() != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            content.add("创建时间：" + dateFormat.format(riskConfig.getCreatedTime()));
        }

        if (StringUtils.hasText(riskConfig.getMatrixDesc())) {
            content.add("矩阵描述：" + riskConfig.getMatrixDesc());
        }

        section.setContent(String.join("\n", content));
        return section;
    }

    /**
     * 构建风险矩阵配置章节
     *
     * @param riskConfig 行业风险配置
     * @return PDF章节
     */
    private PdfUtils.PdfSection buildMatrixConfigSection(IndustryRiskConfig riskConfig) {
        PdfUtils.PdfSection section = new PdfUtils.PdfSection();
        section.setSectionTitle("风险矩阵配置");

        List<String> content = new ArrayList<>();
        RiskMatrixConfig matrixConfig = riskConfig.getMatrixConfig();

        if (matrixConfig != null) {
            content.add("矩阵配置详情：");

            // 展示列定义信息
            if (matrixConfig.getColumnDefs() != null && !matrixConfig.getColumnDefs().isEmpty()) {
                content.add("");
                content.add("列定义配置：");
                for (int i = 0; i < matrixConfig.getColumnDefs().size(); i++) {
                    DynamicColumnDef columnDef = matrixConfig.getColumnDefs().get(i);
                    content.add(String.format("  %d. %s (%s) - 类型：%s",
                            i + 1,
                            columnDef.getLabel() != null ? columnDef.getLabel() : "",
                            columnDef.getKey() != null ? columnDef.getKey() : "",
                            columnDef.getType() != null ? columnDef.getType() : "input"));
                }
            }

            // 展示行数据信息
            if (matrixConfig.getRowData() != null && !matrixConfig.getRowData().isEmpty()) {
                content.add("");
                content.add(String.format("配置数据：共 %d 条记录", matrixConfig.getRowData().size()));

                // 展示前几条数据作为示例（最多5条）
                int maxRows = Math.min(5, matrixConfig.getRowData().size());
                for (int i = 0; i < maxRows; i++) {
                    Map<String, Object> rowData = matrixConfig.getRowData().get(i);
                    content.add(String.format("  记录 %d：", i + 1));

                    // 展示每行的关键字段
                    for (Map.Entry<String, Object> entry : rowData.entrySet()) {
                        if (entry.getValue() != null && StringUtils.hasText(entry.getValue().toString())) {
                            content.add(String.format("    %s: %s", entry.getKey(), entry.getValue()));
                        }
                    }
                }

                if (matrixConfig.getRowData().size() > 5) {
                    content.add(String.format("  ... 还有 %d 条记录", matrixConfig.getRowData().size() - 5));
                }
            } else {
                content.add("");
                content.add("暂无配置数据");
            }

        } else {
            content.add("暂无风险矩阵配置");
        }

        section.setContent(String.join("\n", content));
        return section;
    }
    /**
     * 构建富文本内容章节
     *
     * @param riskConfig 行业风险配置
     * @return PDF章节
     */
    private PdfUtils.PdfSection buildRichTextSection(IndustryRiskConfig riskConfig) {
        PdfUtils.PdfSection section = new PdfUtils.PdfSection();
        section.setSectionTitle("详细说明");

        List<String> content = new ArrayList<>();

        if (StringUtils.hasText(riskConfig.getRichTextContent())) {
            // 简单处理富文本内容，去除HTML标签
            String plainText = riskConfig.getRichTextContent()
                    .replaceAll("<[^>]+>", "") // 去除HTML标签
                    .replaceAll("&nbsp;", " ") // 替换空格实体
                    .replaceAll("&lt;", "<")   // 替换小于号实体
                    .replaceAll("&gt;", ">")   // 替换大于号实体
                    .replaceAll("&amp;", "&")  // 替换和号实体
                    .trim();

            if (StringUtils.hasText(plainText)) {
                // 按段落分割内容
                String[] paragraphs = plainText.split("\\n\\s*\\n");
                for (String paragraph : paragraphs) {
                    if (StringUtils.hasText(paragraph.trim())) {
                        content.add(paragraph.trim());
                    }
                }
            }
        }

        if (content.isEmpty()) {
            content.add("暂无详细说明内容");
        }

        section.setContent(String.join("\n", content));
        return section;
    }

    /**
     * 生成PDF文件名
     *
     * @param riskConfig 行业风险配置
     * @return 文件名
     */
    private String generatePdfFileName(IndustryRiskConfig riskConfig) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String timestamp = dateFormat.format(new Date());

        String industryName = riskConfig.getIndustryLevel1Name();
        if (!StringUtils.hasText(industryName)) {
            industryName = riskConfig.getIndustryLevel1Code();
        }
        if (!StringUtils.hasText(industryName)) {
            industryName = "未知行业";
        }

        // 清理文件名中的特殊字符
        industryName = industryName.replaceAll("[\\\\/:*?\"<>|]", "_");

        return String.format("行业风险配置报告_%s_%s.pdf", industryName, timestamp);
    }
    /**
     * 上传PDF到OSS
     *
     * @param pdfFile PDF文件（MultipartFile类型）
     * @param fileName 文件名
     * @return 上传响应
     */
    private FileUploadResponse uploadPdfToOss(MultipartFile pdfFile, String fileName) {
        try {
            // 生成业务编号
            String businessNo = "INDUSTRY_RISK_CONFIG_PDF_" + System.currentTimeMillis();

            // 构建文件路径
            String filePath = "industry-risk-config/reports/" + fileName;

            // 调用KbcUfsService上传文件
            FileUploadResponse response = kbcUfsService.upload(
                APP_CODE,                    // 应用编码
                businessNo,                  // 业务编号
                pdfFile,                     // MultipartFile对象
                null,                       // content为null，使用MultipartFile上传
                filePath,                   // 文件路径
                FileTypeEnum.FILE           // 文件类型
            );

            if (response == null) {
                throw new RuntimeException("文件上传响应为空");
            }

            log.info("PDF文件上传成功，文件路径：{}, 业务编号：{}", response.getAbsolutePath(), businessNo);
            return response;

        } catch (Exception e) {
            log.error("PDF文件上传失败，文件名：{}", fileName, e);
            throw new RuntimeException("PDF文件上传失败：" + e.getMessage());
        }
    }
}
