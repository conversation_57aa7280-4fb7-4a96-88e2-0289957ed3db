package com.kbao.kbcelms.opportunity.vo;

import com.kbao.commons.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 机会数据导出VO
 * 用于Excel导出功能
 */
@Data
@ApiModel(description = "机会数据导出VO")
public class OpportunityExportVO {
    
    @Excel(name = "机会ID")
    private Integer id;
    
    @Excel(name = "机会名称")
    private String opportunityName;
    
    @Excel(name = "代理人编码")
    private String agentCode;
    
    @Excel(name = "代理人姓名")
    private String agentName;
    
    @Excel(name = "公司编码")
    private String companyCode;
    
    @Excel(name = "公司名称")
    private String companyName;
    
    @Excel(name = "交易中心名称")
    private String tradingCenterName;
    
    @Excel(name = "销售中心名称")
    private String salesCenterName;
    
    @Excel(name = "机会类型")
    private String opportunityType;
    
    @Excel(name = "行业编码")
    private String industryCode;
    
    @Excel(name = "创建时间")
    private Date createTime;
    
    @Excel(name = "机会状态")
    private String statusText;

    private Integer status;
    
    @Excel(name = "锁定状态")
    private String lockStatusText;

    private Integer lockStatus;
    
    @Excel(name = "业务编码")
    private String bizCode;
    
    @Excel(name = "区域中心编码")
    private String areaCenterCode;
    
    @Excel(name = "区域中心名称")
    private String areaCenterName;
    
    @Excel(name = "法人编码")
    private String legalCode;
    
    @Excel(name = "法人姓名")
    private String legalName;
    
    @Excel(name = "流程步骤")
    private String processStep;
    
    @Excel(name = "统筹人员")
    private String coordinator;
    
    @Excel(name = "项目经理")
    private String projectManager;
    
    @Excel(name = "项目组织编码")
    private String projectOrgCode;
    
    @Excel(name = "项目组织名称")
    private String projectOrgName;
    
    @Excel(name = "客户需求")
    private String generalInsuranceName;
    
    @Excel(name = "投保人数")
    private Integer insureNum;
    
    @Excel(name = "保费预算")
    private BigDecimal premiumBudget;
    
    @Excel(name = "是否投标")
    private String isBidText;

    private Integer isBid;
    
    @Excel(name = "提交时间")
    private Date submitTime;
    
    @Excel(name = "组队时间")
    private Date teamTime;
    
    @Excel(name = "日志时间")
    private Date logTime;
    
    @Excel(name = "企业对接人")
    private String contacter;
    
    @Excel(name = "企业对接人职务")
    private String contacterPost;
    
    @Excel(name = "是否添加健康服务")
    private String addHealthService;
    
    @Excel(name = "健康服务编码")
    private String healthServiceCode;
    
    @Excel(name = "健康服务名称")
    private String healthServiceName;
    
    @Excel(name = "是否添加救援服务")
    private String addRescueService;
    
    @Excel(name = "救援服务编码")
    private String rescueServiceCode;
    
    @Excel(name = "救援服务名称")
    private String rescueServiceName;
    
    @Excel(name = "员工保险类型")
    private String employeeInsuranceType;
    
    @Excel(name = "通用保险类型")
    private String generalInsuranceType;
    
    @Excel(name = "备注")
    private String remark;
    
    @Excel(name = "是否有历史保单")
    private String hasHistoryPolicyText;
    
    @Excel(name = "保单到期时间")
    private Date policyExpireTime;
    
    @Excel(name = "投标结果")
    private Integer bidResult;
    
    @Excel(name = "投标开始时间")
    private Date bidStartDate;
    
    @Excel(name = "投标结束时间")
    private Date bidEndDate;
    
    @Excel(name = "企业ID")
    private Integer enterpriseId;
    
    @Excel(name = "企业名称")
    private String enterpriseName;
    
    @Excel(name = "社会统一信用代码")
    private String creditCode;
    
    @Excel(name = "企业类型")
    private String dtTypeText;
    
    @Excel(name = "行业类别编码")
    private String categoryCode;
    
    @Excel(name = "行业类别名称")
    private String categoryName;
    
    @Excel(name = "企业规模")
    private String enterpriseScale;
    
    @Excel(name = "企业所在城市")
    private String city;
    
    @Excel(name = "企业人员规模")
    private String staffScale;
    
    @Excel(name = "企业年收入")
    private String annualIncome;
    
    @Excel(name = "是否认证")
    private String isVerified;
    
    @Excel(name = "企业联系人")
    private String enterpriseContacter;
    
    @Excel(name = "联系人电话")
    private String contacterPhone;
    
    @Excel(name = "企业备注")
    private String enterpriseRemark;
    
    @Excel(name = "项目经理姓名")
    private String projectManagerName;
    
    @Excel(name = "项目负责人电话")
    private String projectManagerPhone;
    
    @Excel(name = "项目负责人邮箱")
    private String projectManagerEmail;
    
    @Excel(name = "统筹人员姓名")
    private String coordinatorName;
    
    @Excel(name = "统筹人员电话")
    private String coordinatorPhone;
    
    @Excel(name = "统筹人员邮箱")
    private String coordinatorEmail;
    
    @Excel(name = "KYC报告生成时间")
    private Date kycReportTime;
    
    @Excel(name = "咨询风险报告生成时间")
    private Date riskReportTime;
    
    @Excel(name = "统筹领取时间")
    private Date coordinationAcceptTime;
    
    @Excel(name = "指派统筹时间")
    private Date assignCoordinationTime;
    
    @Excel(name = "指派项目经理时间")
    private Date assignProjectManagerTime;
    
    @Excel(name = "项目人员/分工/比例")
    private String projectTeamInfo;
    
    @Excel(name = "机会关闭时间")
    private Date closeTime;
    
    @Excel(name = "项目总结时间")
    private Date summaryTime;
    
    @Excel(name = "排分时间")
    private Date rankingTime;
    
    @Excel(name = "出单时间")
    private Date policyTime;
    
    @Excel(name = "暂停时间")
    private Date suspendTime;
    
    @Excel(name = "重启时间")
    private Date restartTime;
    
    /**
     * 状态映射
     */
    public static String getStatusText(Integer status) {
        if (status == null) {
            return "";
        }
        switch (status) {
            case 1:
                return "进行中";
            case 2:
                return "已完成";
            case 3:
                return "已终止";
            case 4:
                return "已暂停";
            case 5:
                return "已关闭";
            default:
                return "未知状态";
        }
    }
    
    /**
     * 投标状态映射
     */
    public static String getIsBidText(Integer isBid) {
        if (isBid == null) {
            return "";
        }
        return isBid == 1 ? "是" : "否";
    }
    
    /**
     * 历史保单状态映射
     */
    public static String getHasHistoryPolicyText(Integer hasHistoryPolicy) {
        if (hasHistoryPolicy == null) {
            return "";
        }
        return hasHistoryPolicy == 1 ? "有" : "无";
    }
    
    /**
     * 企业类型映射
     */
    public static String getDtTypeText(String dtType) {
        if (dtType == null) {
            return "";
        }
        switch (dtType) {
            case "A":
                return "央企";
            case "B":
                return "上市公司";
            case "C":
                return "大型企业";
            case "D":
                return "中小企业";
            default:
                return dtType;
        }
    }
    
    /**
     * 机会类型映射
     */
    public static String getOpportunityTypeText(String opportunityType) {
        if (opportunityType == null) {
            return "";
        }
        switch (opportunityType) {
            case "1":
                return "员工保险";
            case "2":
                return "通用保险";
            default:
                return opportunityType;
        }
    }
    
    /**
     * 是否添加服务映射
     */
    public static String getServiceText(String serviceFlag) {
        if (serviceFlag == null) {
            return "";
        }
        return "1".equals(serviceFlag) ? "是" : "否";
    }
    
    /**
     * 是否认证映射
     */
    public static String getVerifiedText(String isVerified) {
        if (isVerified == null) {
            return "";
        }
        return "1".equals(isVerified) ? "是" : "否";
    }
} 