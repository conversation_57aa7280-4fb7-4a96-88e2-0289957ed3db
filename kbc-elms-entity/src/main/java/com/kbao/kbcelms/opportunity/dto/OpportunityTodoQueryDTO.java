package com.kbao.kbcelms.opportunity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 机会待办任务查询参数DTO
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ApiModel(description = "机会待办任务查询参数")
public class OpportunityTodoQueryDTO {
    
    /** 租户ID */
    @ApiModelProperty(value = "租户ID", required = true)
    private String tenantId;
    
    /** 用户ID */
    @ApiModelProperty(value = "用户ID", required = true)
    private String userId;
    
    /** 流程实例ID列表 */
    @ApiModelProperty(value = "流程实例ID列表")
    private List<String> processInstanceIds;
    
    /** 顾问姓名 */
    @ApiModelProperty(value = "顾问姓名")
    private String agentName;
    
    /** 所属机构 */
    @ApiModelProperty(value = "所属机构")
    private String legalCode;
    
    /** 机会名称 */
    @ApiModelProperty(value = "机会名称")
    private String opportunityName;
    
    /** 机会ID */
    @ApiModelProperty(value = "机会ID")
    private Integer opportunityId;
    
    /** 机会提交时间开始 */
    @ApiModelProperty(value = "机会提交时间开始")
    private Date submitTimeStart;
    
    /** 机会提交时间结束 */
    @ApiModelProperty(value = "机会提交时间结束")
    private Date submitTimeEnd;
    
    /** 企业名称 */
    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;
    
    /** 企业代码 */
    @ApiModelProperty(value = "企业代码")
    private String creditCode;
    
    /** 机会状态 */
    @ApiModelProperty(value = "机会状态")
    private Integer status;

    /** 机会类型: 1-员服，2-综合 */
    @ApiModelProperty(value = "机会类型", example = "1", notes = "1-员服，2-综合")
    private String opportunityType;

    /** 机会关闭原因类型：1-机会已成交，2-机会推进失败，3-无效机会 */
    @ApiModelProperty(value = "机会关闭原因类型", example = "1", notes = "1-机会已成交，2-机会推进失败，3-无效机会")
    private Integer closeReasonType;

    @ApiModelProperty(value = "流程步骤  status-processStep")
    private String processStep;
    
    /** 计划编码 */
    @ApiModelProperty(value = "计划编码", example = "PLAN001")
    private String bizCode;
}
